# Prerequisites
Before we can jump head first into working a template, we need to go over a few things.

* **AN EXPERIENCED MODDER** You should be able to mod any games in general, like modifying .so files, dll files, smali files, etc.
* Basic knowledge of smali dalvik opcodes to modify smali
* Basic knowledge ARM and ARM64 assembly, to be able to patch hex (No need x86)
* Basic knowledge of C++ and java (JNI is optional)
* Be able to write hook function in C++ (Not really needed, but recommended if you want to do advanced modding in the future)
* Basic awareness of how Android layout works in XML and Java. This project only use Java for layout but you will learn it easly
* Time and patience: Don't start working on this if you have deadlines or important work. Take your time to read, learn and get used to work with this project.
* DIY (Do it yourself): Yes, you must be able to do things yourself, not depending being spoonfeed. We are not the teachers.
* An inquisitive mind

We are not babysitters for naive, lazy, pathetic children... Grow up. Read and learning by yourself is mandatory!

# Getting started

1. Installation
    * [PC](Installation-(PC))
    * [Android](Installation-(Android))
2. [Usage](Usage)
3. Making changes to the APK
    * [PC](Making-changes-to-the-APK-(PC))
    * [Android](Making-changes-to-the-APK-(Android))
4. Troubleshooting
    * [PC](Troubleshooting-(PC))
    * [Android](Troubleshooting-(Android))

# Video tutorial

You can watch the video tutorials below. Be warned, those videos might be outdated

Huge thanks to them.

### PC

PMT DVA: https://www.youtube.com/watch?v=ieMclBtL6Ig

Pasha Production: https://www.youtube.com/watch?v=RvrZKIe-QGc

### Android
Mahmoud Gaming: https://www.youtube.com/watch?v=SMCsUy60Hs8

NSRAÎNA HACKER: https://www.youtube.com/watch?v=MkkZ_loEDTU

BROKE MODS OFC (Customized menu): https://www.youtube.com/watch?v=IYREVGc-quM