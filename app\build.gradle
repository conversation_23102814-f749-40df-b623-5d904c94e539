apply plugin: 'com.android.application'

android {
    compileSdkVersion 34
    defaultConfig {
        applicationId "com.android.support"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "4.0"
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }
    buildTypes {
        release {
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        ndkBuild {
            path file('src/main/jni/Android.mk')
        }
    }
    namespace 'com.android.support'
}

//dependencies must be placed below 'android' brackets to get it work on AIDE
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}